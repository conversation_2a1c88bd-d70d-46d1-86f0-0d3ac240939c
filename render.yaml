services:
  - type: web
    name: gamyday-notification-server
    env: python
    plan: free
    buildCommand: pip install -r requirements.txt
    startCommand: python main.py
    envVars:
      - key: MONGODB_URL
        sync: false
      - key: VAPID_PUBLIC_KEY
        sync: false
      - key: VAPID_PRIVATE_KEY
        sync: false
      - key: VAPID_SUBJECT
        sync: false
      - key: ALLOWED_ORIGINS
        value: https://your-frontend-domain.com,http://localhost:3000
      - key: ADMIN_USERNAME
        value: admin
      - key: ADMIN_PASSWORD
        sync: false
      - key: SECRET_KEY
        generateValue: true
      - key: DEBUG
        value: false
      - key: PORT
        value: 10000
      - key: HOST
        value: 0.0.0.0
