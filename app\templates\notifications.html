{% extends "base.html" %}

{% block title %}Notification History - GamyDay Notification Admin{% endblock %}

{% block content %}
<div class="space-y-6">
    <!-- Page Header -->
    <div class="flex justify-between items-center">
        <h2 class="text-2xl font-bold text-gray-900">Notification History</h2>
        <div class="text-sm text-gray-500">
            Showing last {{ notifications|length }} notifications
        </div>
    </div>

    <!-- Notifications List -->
    <div class="bg-white rounded-lg shadow">
        {% if notifications %}
        <div class="divide-y divide-gray-200">
            {% for notification in notifications %}
            <div class="p-6">
                <div class="flex items-start justify-between">
                    <div class="flex-1">
                        <div class="flex items-center space-x-3">
                            <h3 class="text-lg font-medium text-gray-900">{{ notification.payload.title }}</h3>
                            {% if notification.status == 'sent' %}
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                <i class="fas fa-check mr-1"></i>
                                Sent
                            </span>
                            {% elif notification.status == 'failed' %}
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
                                <i class="fas fa-times mr-1"></i>
                                Failed
                            </span>
                            {% else %}
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                                <i class="fas fa-clock mr-1"></i>
                                Pending
                            </span>
                            {% endif %}
                        </div>
                        
                        <p class="text-gray-600 mt-2">{{ notification.payload.body }}</p>
                        
                        <div class="flex items-center mt-4 space-x-6 text-sm text-gray-500">
                            <span>
                                <i class="fas fa-calendar mr-1"></i>
                                Created: {{ notification.created_at.strftime('%Y-%m-%d %H:%M:%S') }}
                            </span>
                            {% if notification.sent_at %}
                            <span>
                                <i class="fas fa-paper-plane mr-1"></i>
                                Sent: {{ notification.sent_at.strftime('%Y-%m-%d %H:%M:%S') }}
                            </span>
                            {% endif %}
                            <span>
                                <i class="fas fa-bullseye mr-1"></i>
                                Target: {{ notification.target_type.title() }}
                            </span>
                        </div>
                        
                        <div class="flex items-center mt-2 space-x-6 text-sm">
                            <span class="text-green-600">
                                <i class="fas fa-check-circle mr-1"></i>
                                {{ notification.sent_count }} sent
                            </span>
                            {% if notification.failed_count > 0 %}
                            <span class="text-red-600">
                                <i class="fas fa-exclamation-circle mr-1"></i>
                                {{ notification.failed_count }} failed
                            </span>
                            {% endif %}
                        </div>
                        
                        {% if notification.error_message %}
                        <div class="mt-2 p-3 bg-red-50 border border-red-200 rounded-lg">
                            <p class="text-sm text-red-700">
                                <i class="fas fa-exclamation-triangle mr-1"></i>
                                {{ notification.error_message }}
                            </p>
                        </div>
                        {% endif %}
                        
                        <!-- Additional Details -->
                        <div class="mt-4 p-4 bg-gray-50 rounded-lg">
                            <h4 class="text-sm font-medium text-gray-700 mb-2">Notification Details</h4>
                            <div class="grid grid-cols-2 gap-4 text-sm">
                                {% if notification.payload.url %}
                                <div>
                                    <span class="text-gray-500">Click URL:</span>
                                    <a href="{{ notification.payload.url }}" target="_blank" class="text-blue-600 hover:text-blue-800 ml-1">
                                        {{ notification.payload.url }}
                                        <i class="fas fa-external-link-alt ml-1"></i>
                                    </a>
                                </div>
                                {% endif %}
                                {% if notification.payload.icon %}
                                <div>
                                    <span class="text-gray-500">Icon:</span>
                                    <span class="ml-1">{{ notification.payload.icon }}</span>
                                </div>
                                {% endif %}
                                {% if notification.payload.tag %}
                                <div>
                                    <span class="text-gray-500">Tag:</span>
                                    <span class="ml-1">{{ notification.payload.tag }}</span>
                                </div>
                                {% endif %}
                                <div>
                                    <span class="text-gray-500">Require Interaction:</span>
                                    <span class="ml-1">{{ 'Yes' if notification.payload.require_interaction else 'No' }}</span>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="ml-6 flex-shrink-0">
                        <button onclick="toggleDetails('{{ notification._id }}')" class="text-gray-400 hover:text-gray-600">
                            <i class="fas fa-chevron-down" id="chevron-{{ notification._id }}"></i>
                        </button>
                    </div>
                </div>
                
                <!-- Expandable Details -->
                <div id="details-{{ notification._id }}" class="hidden mt-4 p-4 bg-gray-50 rounded-lg">
                    <h4 class="text-sm font-medium text-gray-700 mb-2">Raw Data</h4>
                    <pre class="text-xs text-gray-600 overflow-x-auto">{{ notification | tojson(indent=2) }}</pre>
                </div>
            </div>
            {% endfor %}
        </div>
        {% else %}
        <div class="text-center py-12">
            <i class="fas fa-bell-slash text-gray-400 text-4xl mb-4"></i>
            <h3 class="text-lg font-medium text-gray-900 mb-2">No Notifications</h3>
            <p class="text-gray-500">No notifications have been sent yet.</p>
            <a href="/admin/send-notification" class="mt-4 inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700">
                <i class="fas fa-paper-plane mr-2"></i>
                Send Your First Notification
            </a>
        </div>
        {% endif %}
    </div>
</div>

<script>
    function toggleDetails(notificationId) {
        const details = document.getElementById(`details-${notificationId}`);
        const chevron = document.getElementById(`chevron-${notificationId}`);
        
        if (details.classList.contains('hidden')) {
            details.classList.remove('hidden');
            chevron.classList.remove('fa-chevron-down');
            chevron.classList.add('fa-chevron-up');
        } else {
            details.classList.add('hidden');
            chevron.classList.remove('fa-chevron-up');
            chevron.classList.add('fa-chevron-down');
        }
    }
</script>
{% endblock %}
