{% extends "base.html" %}

{% block title %}Subscriptions - GamyDay Notification Admin{% endblock %}

{% block content %}
<div class="space-y-6">
    <!-- Page Header -->
    <div class="flex justify-between items-center">
        <h2 class="text-2xl font-bold text-gray-900">Subscriptions</h2>
        <div class="text-sm text-gray-500">
            Total: {{ subscriptions|length }} active subscriptions
        </div>
    </div>

    <!-- Subscriptions Table -->
    <div class="bg-white rounded-lg shadow overflow-hidden">
        {% if subscriptions %}
        <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-200">
                <thead class="bg-gray-50">
                    <tr>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Subscription
                        </th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Registered
                        </th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Last Notification
                        </th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Count
                        </th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Status
                        </th>
                    </tr>
                </thead>
                <tbody class="bg-white divide-y divide-gray-200">
                    {% for subscription in subscriptions %}
                    <tr class="hover:bg-gray-50">
                        <td class="px-6 py-4">
                            <div class="text-sm text-gray-900">
                                <div class="font-medium">{{ subscription.subscription.endpoint.split('/')[-1][:20] }}...</div>
                                <div class="text-gray-500 text-xs">
                                    {% if subscription.user_agent %}
                                    {{ subscription.user_agent.split(' ')[0] }}
                                    {% endif %}
                                </div>
                            </div>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                            {{ subscription.timestamp.strftime('%Y-%m-%d %H:%M') }}
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                            {% if subscription.last_notification %}
                            {{ subscription.last_notification.strftime('%Y-%m-%d %H:%M') }}
                            {% else %}
                            <span class="text-gray-400">Never</span>
                            {% endif %}
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                            {{ subscription.notification_count or 0 }}
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            {% if subscription.is_active %}
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                <i class="fas fa-check mr-1"></i>
                                Active
                            </span>
                            {% else %}
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
                                <i class="fas fa-times mr-1"></i>
                                Inactive
                            </span>
                            {% endif %}
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
        {% else %}
        <div class="text-center py-12">
            <i class="fas fa-users-slash text-gray-400 text-4xl mb-4"></i>
            <h3 class="text-lg font-medium text-gray-900 mb-2">No Subscriptions</h3>
            <p class="text-gray-500">No users have subscribed to notifications yet.</p>
        </div>
        {% endif %}
    </div>

    <!-- Export Options -->
    {% if subscriptions %}
    <div class="bg-white rounded-lg shadow p-6">
        <h3 class="text-lg font-semibold text-gray-900 mb-4">Export Options</h3>
        <div class="flex space-x-4">
            <button onclick="exportSubscriptions('csv')" class="px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700">
                <i class="fas fa-file-csv mr-2"></i>
                Export as CSV
            </button>
            <button onclick="exportSubscriptions('json')" class="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700">
                <i class="fas fa-file-code mr-2"></i>
                Export as JSON
            </button>
        </div>
    </div>
    {% endif %}
</div>

<script>
    function exportSubscriptions(format) {
        const subscriptions = {{ subscriptions | tojson }};
        
        if (format === 'csv') {
            exportAsCSV(subscriptions);
        } else if (format === 'json') {
            exportAsJSON(subscriptions);
        }
    }
    
    function exportAsCSV(subscriptions) {
        const headers = ['Endpoint', 'Registered', 'Last Notification', 'Count', 'Status', 'User Agent'];
        const csvContent = [
            headers.join(','),
            ...subscriptions.map(sub => [
                `"${sub.subscription.endpoint}"`,
                `"${sub.timestamp}"`,
                `"${sub.last_notification || ''}"`,
                sub.notification_count || 0,
                sub.is_active ? 'Active' : 'Inactive',
                `"${sub.user_agent || ''}"`
            ].join(','))
        ].join('\n');
        
        downloadFile(csvContent, 'subscriptions.csv', 'text/csv');
    }
    
    function exportAsJSON(subscriptions) {
        const jsonContent = JSON.stringify(subscriptions, null, 2);
        downloadFile(jsonContent, 'subscriptions.json', 'application/json');
    }
    
    function downloadFile(content, filename, contentType) {
        const blob = new Blob([content], { type: contentType });
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = filename;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        window.URL.revokeObjectURL(url);
    }
</script>
{% endblock %}
