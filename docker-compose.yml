version: '3.8'

services:
  notification-server:
    build: .
    ports:
      - "8000:8000"
    environment:
      - MONGODB_URL=mongodb://mongo:27017/gamyday_notifications
      - VAPID_PUBLIC_KEY=${VAPID_PUBLIC_KEY}
      - VAPID_PRIVATE_KEY=${VAPID_PRIVATE_KEY}
      - VAPID_SUBJECT=${VAPID_SUBJECT}
      - ALLOWED_ORIGINS=http://localhost:3000
      - ADMIN_USERNAME=admin
      - ADMIN_PASSWORD=admin123
      - SECRET_KEY=your-secret-key-change-this
      - DEBUG=true
    depends_on:
      - mongo
    volumes:
      - .:/app
    restart: unless-stopped

  mongo:
    image: mongo:7
    ports:
      - "27017:27017"
    volumes:
      - mongo_data:/data/db
    restart: unless-stopped

volumes:
  mongo_data:
