#!/usr/bin/env python3
"""
<PERSON><PERSON><PERSON> to set up the MongoDB database for the notification server.
This script creates the necessary collections and indexes.
"""

import asyncio
import sys
import os

# Add the parent directory to the path so we can import our modules
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from app.database import db
from app.config import settings


async def setup_database():
    """Set up the database with collections and indexes."""
    print("Setting up MongoDB database...")
    print(f"Database: {settings.database_name}")
    print(f"MongoDB URL: {settings.mongodb_url}")
    print("=" * 60)
    
    try:
        # Connect to database
        print("📡 Connecting to MongoDB...")
        await db.connect()
        print("✅ Connected successfully!")
        
        # Create collections if they don't exist
        print("\n📁 Creating collections...")
        
        # Get existing collections
        existing_collections = await db.database.list_collection_names()
        
        collections_to_create = ["subscriptions", "notifications"]
        
        for collection_name in collections_to_create:
            if collection_name not in existing_collections:
                await db.database.create_collection(collection_name)
                print(f"✅ Created collection: {collection_name}")
            else:
                print(f"ℹ️  Collection already exists: {collection_name}")
        
        # Create indexes
        print("\n🔍 Creating indexes...")
        await db.create_indexes()
        print("✅ Indexes created successfully!")
        
        # Test the setup
        print("\n🧪 Testing database setup...")
        
        # Test subscriptions collection
        subscription_count = await db.database.subscriptions.count_documents({})
        print(f"📊 Subscriptions collection: {subscription_count} documents")
        
        # Test notifications collection
        notification_count = await db.database.notifications.count_documents({})
        print(f"📊 Notifications collection: {notification_count} documents")
        
        print("\n✅ Database setup completed successfully!")
        print("\n📋 Next steps:")
        print("1. Update your .env file with the correct MongoDB URL")
        print("2. Generate VAPID keys using: python scripts/generate_vapid_keys.py")
        print("3. Start the server using: python main.py")
        
    except Exception as e:
        print(f"❌ Error setting up database: {e}")
        return 1
    
    finally:
        # Disconnect from database
        await db.disconnect()
        print("\n📡 Disconnected from MongoDB")
    
    return 0


def main():
    """Main function."""
    return asyncio.run(setup_database())


if __name__ == "__main__":
    exit(main())
